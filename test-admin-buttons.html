<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Button Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Admin Button Functionality Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Logout Button Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Logout Button Test</h2>
                <button id="test-logout" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Test Logout
                </button>
                <p class="text-sm text-gray-600 mt-2">Should show confirmation dialog</p>
            </div>
            
            <!-- Edit Button Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Edit Button Test</h2>
                <button onclick="editUser(1)" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <i class="fas fa-edit mr-2"></i>
                    Test Edit User
                </button>
                <p class="text-sm text-gray-600 mt-2">Should open edit modal</p>
            </div>
            
            <!-- Delete Button Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Delete Button Test</h2>
                <button onclick="deleteUser(1)" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <i class="fas fa-trash mr-2"></i>
                    Test Delete User
                </button>
                <p class="text-sm text-gray-600 mt-2">Should show confirmation dialog</p>
            </div>
            
            <!-- Console Log -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Console Output</h2>
                <div id="console-output" class="bg-gray-100 p-4 rounded text-sm font-mono h-32 overflow-y-auto">
                    <!-- Console messages will appear here -->
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
            <strong>Instructions:</strong>
            <ol class="list-decimal list-inside mt-2">
                <li>Open browser developer tools (F12)</li>
                <li>Go to the Console tab</li>
                <li>Click each test button</li>
                <li>Check if functions are defined and working</li>
            </ol>
        </div>
    </div>

    <script src="api-config.js"></script>
    <script src="admin.js"></script>
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            consoleOutput.innerHTML += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };
        
        // Test logout button
        document.getElementById('test-logout').addEventListener('click', function() {
            console.log('Testing logout button...');
            if (typeof window.adminDashboard !== 'undefined' && window.adminDashboard.handleLogout) {
                console.log('✓ adminDashboard.handleLogout exists');
                // Don't actually call it to avoid logout
                console.log('✓ Logout function is accessible');
            } else {
                console.log('✗ adminDashboard.handleLogout not found');
            }
        });
        
        // Test if global functions exist
        setTimeout(() => {
            console.log('=== Testing Global Functions ===');
            console.log('editUser function:', typeof window.editUser);
            console.log('deleteUser function:', typeof window.deleteUser);
            console.log('closeEditUserModal function:', typeof window.closeEditUserModal);
            console.log('adminDashboard object:', typeof window.adminDashboard);
            
            if (typeof window.editUser === 'function') {
                console.log('✓ editUser is accessible globally');
            } else {
                console.log('✗ editUser is not accessible globally');
            }
            
            if (typeof window.deleteUser === 'function') {
                console.log('✓ deleteUser is accessible globally');
            } else {
                console.log('✗ deleteUser is not accessible globally');
            }
        }, 1000);
    </script>
</body>
</html>
