{"name": "csurf", "description": "CSRF token middleware", "version": "1.11.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "expressjs/csurf", "dependencies": {"cookie": "0.4.0", "cookie-signature": "1.0.6", "csrf": "3.1.0", "http-errors": "~1.7.3"}, "devDependencies": {"body-parser": "1.19.0", "connect": "3.7.0", "cookie-parser": "1.4.4", "cookie-session": "1.4.0", "eslint": "6.8.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.20.0", "eslint-plugin-markdown": "1.0.1", "eslint-plugin-node": "11.0.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "7.0.0", "nyc": "15.0.0", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "keywords": ["csrf", "tokens", "middleware", "express"]}