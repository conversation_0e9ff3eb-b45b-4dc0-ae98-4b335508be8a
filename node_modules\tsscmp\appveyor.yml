# Test against this version of Node.js
environment:
  matrix:
   # nodejs_version: "0.6" not supported  in Windows x86
   - nodejs_version: "0.8"
   - nodejs_version: "0.10"
   - nodejs_version: "0.11"
   - nodejs_version: "0.12"
   - nodejs_version: "4.0"
   - nodejs_version: "5.0"
   - nodejs_version: "6.0"

# Install scripts. (runs after repo cloning)
install:
  # Get the latest stable version of Node.js or io.js
  - ps: Install-Product node $env:nodejs_version
  # install modules
  - npm install

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  # run tests
  - npm test

# Don't actually build.
build: off
