// Admin Button Test Script
// Run this in the browser console on the admin page

console.log('=== ADMIN BUTTON FUNCTIONALITY TEST ===');

// Test 1: Check if adminDashboard object exists
console.log('\n1. Testing adminDashboard object:');
if (typeof window.adminDashboard !== 'undefined') {
    console.log('✓ window.adminDashboard exists');
    console.log('✓ Type:', typeof window.adminDashboard);
} else {
    console.log('✗ window.adminDashboard not found');
}

// Test 2: Check if global functions exist
console.log('\n2. Testing global functions:');
const globalFunctions = ['editUser', 'deleteUser', 'closeEditUserModal', 'viewOrder', 'updateOrderStatus'];
globalFunctions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✓ ${funcName} is accessible globally`);
    } else {
        console.log(`✗ ${funcName} is not accessible globally`);
    }
});

// Test 3: Check if logout button exists and has event listener
console.log('\n3. Testing logout button:');
const logoutBtn = document.getElementById('admin-logout');
if (logoutBtn) {
    console.log('✓ Logout button found');
    console.log('✓ Button element:', logoutBtn);
    console.log('✓ Button text:', logoutBtn.textContent.trim());
    
    // Check if button is clickable
    const rect = logoutBtn.getBoundingClientRect();
    if (rect.width > 0 && rect.height > 0) {
        console.log('✓ Button is visible');
    } else {
        console.log('✗ Button is not visible');
    }
} else {
    console.log('✗ Logout button not found');
}

// Test 4: Check if edit/delete buttons exist in user table
console.log('\n4. Testing user table buttons:');
const editButtons = document.querySelectorAll('button[onclick*="editUser"]');
const deleteButtons = document.querySelectorAll('button[onclick*="deleteUser"]');
console.log(`✓ Found ${editButtons.length} edit buttons`);
console.log(`✓ Found ${deleteButtons.length} delete buttons`);

if (editButtons.length > 0) {
    console.log('✓ Edit buttons found in user table');
    console.log('✓ First edit button onclick:', editButtons[0].getAttribute('onclick'));
} else {
    console.log('✗ No edit buttons found');
}

if (deleteButtons.length > 0) {
    console.log('✓ Delete buttons found in user table');
    console.log('✓ First delete button onclick:', deleteButtons[0].getAttribute('onclick'));
} else {
    console.log('✗ No delete buttons found');
}

// Test 5: Test logout button click (without actually logging out)
console.log('\n5. Testing logout button click simulation:');
if (logoutBtn && window.adminDashboard && window.adminDashboard.handleLogout) {
    console.log('✓ Ready to test logout button');
    console.log('Note: Click the logout button manually to test');
} else {
    console.log('✗ Cannot test logout button - missing dependencies');
}

// Test 6: Test edit function call (safe test)
console.log('\n6. Testing edit function call:');
if (typeof window.editUser === 'function') {
    console.log('✓ editUser function is callable');
    console.log('Note: Call editUser(1) manually to test');
} else {
    console.log('✗ editUser function not callable');
}

// Test 7: Check current section
console.log('\n7. Testing current section:');
if (window.adminDashboard && window.adminDashboard.currentSection) {
    console.log('✓ Current section:', window.adminDashboard.currentSection);
} else {
    console.log('✗ Current section not available');
}

console.log('\n=== TEST COMPLETE ===');
console.log('Manual tests to perform:');
console.log('1. Click the logout button in the sidebar');
console.log('2. Navigate to Users section');
console.log('3. Click an Edit button next to a user');
console.log('4. Click a Delete button next to a user');
console.log('5. Check browser console for any errors');
